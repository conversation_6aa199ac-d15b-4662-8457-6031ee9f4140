/**
 * API Client Tests
 * 
 * Comprehensive tests for the HTTP API client including error handling,
 * retry logic, interceptors, and all endpoint methods.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest"

import { ApiClient, createApiClient } from "@/lib/api/client"
import type { QueryRequest, IngestionRequest } from "@/types/api"
import { createMockApiResponse } from "@/test/utils/api-mocks"

// Mock fetch globally
const mockFetch = vi.fn()
vi.stubGlobal('fetch', mockFetch)

describe("ApiClient", () => {
  let client: ApiClient

  beforeEach(() => {
    client = createApiClient({
      baseUrl: "http://localhost:8000",
      timeout: 5000,
      retries: 2,
      enableLogging: false,
      enableRetry: true,
    })
    
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe("constructor", () => {
    it("should create client with default configuration", () => {
      const defaultClient = new ApiClient()
      expect(defaultClient).toBeInstanceOf(ApiClient)
    })

    it("should create client with custom configuration", () => {
      const customClient = createApiClient({
        timeout: 10000,
        retries: 5,
      })
      expect(customClient).toBeInstanceOf(ApiClient)
    })
  })

  describe("interceptors", () => {
    it("should add request interceptors", () => {
      const interceptor = vi.fn((url, options) => ({ url, options }))
      client.addRequestInterceptor(interceptor)
      
      // Interceptor should be added (we can't directly test this without exposing internals)
      expect(interceptor).toBeDefined()
    })

    it("should add response interceptors", () => {
      const interceptor = vi.fn((response) => response)
      client.addResponseInterceptor(interceptor)
      
      // Interceptor should be added
      expect(interceptor).toBeDefined()
    })
  })

  describe("query method", () => {
    it("should make successful query request", async () => {
      const mockResponse = {
        result_markdown: "# Test Response",
        structured: { test: true },
        agent_type: "TECHNICAL_ARCHITECT",
        confidence: 0.95,
        sources: ["test.py:1-10"],
        session_id: "test-session",
      }

      mockFetch.mockResolvedValueOnce(createMockApiResponse(mockResponse))

      const request: QueryRequest = {
        query: "Test query",
        session_id: "test-session",
      }

      const result = await client.query(request)

      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/api/query",
        expect.objectContaining({
          method: "POST",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
          }),
          body: JSON.stringify(request),
        })
      )

      expect(result).toEqual(mockResponse)
    })

    it("should handle query request errors", async () => {
      mockFetch.mockResolvedValueOnce(createMockApiResponse(
        { detail: "Query failed" },
        { status: 500 }
      ))

      const request: QueryRequest = {
        query: "Test query",
      }

      await expect(client.query(request)).rejects.toThrow()
    })
  })

  describe("ingest method", () => {
    it("should make successful ingestion request", async () => {
      const mockResponse = {
        status: "completed",
        repository: "https://github.com/test/repo",
        processed_files: 100,
        chunks_created: 500,
        embeddings_generated: 500,
        processing_time: 30.5,
      }

      mockFetch.mockResolvedValueOnce(createMockApiResponse(mockResponse))

      const request: IngestionRequest = {
        repository_url: "https://github.com/test/repo",
        branch: "main",
      }

      const result = await client.ingest(request)

      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/api/ingest",
        expect.objectContaining({
          method: "POST",
          body: JSON.stringify(request),
        })
      )

      expect(result).toEqual(mockResponse)
    })

    it("should handle ingestion request errors", async () => {
      mockFetch.mockResolvedValueOnce(createMockApiResponse(
        { detail: "Ingestion failed" },
        { status: 400 }
      ))

      const request: IngestionRequest = {
        repository_url: "invalid-url",
      }

      await expect(client.ingest(request)).rejects.toThrow()
    })
  })

  describe("getStatus method", () => {
    it("should fetch system status", async () => {
      const mockResponse = {
        api: "operational",
        agents: {
          ORCHESTRATOR: "operational",
          TECHNICAL_ARCHITECT: "operational",
        },
        vector_store: "operational",
        ingestion_pipeline: "operational",
        active_sessions: 5,
      }

      mockFetch.mockResolvedValueOnce(createMockApiResponse(mockResponse))

      const result = await client.getStatus()

      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/api/status",
        expect.objectContaining({
          method: "GET",
        })
      )

      expect(result).toEqual(mockResponse)
    })
  })

  describe("getHealth method", () => {
    it("should fetch health status", async () => {
      const mockResponse = {
        status: "healthy",
        service: "llm-rag-backend",
        version: "0.1.0",
        environment: "test",
        timestamp: "2024-01-01T00:00:00Z",
      }

      mockFetch.mockResolvedValueOnce(createMockApiResponse(mockResponse))

      const result = await client.getHealth()

      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:8000/health",
        expect.objectContaining({
          method: "GET",
        })
      )

      expect(result).toEqual(mockResponse)
    })
  })

  describe("error handling", () => {
    it("should handle network errors", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Network error"))

      await expect(client.getHealth()).rejects.toThrow("Network error")
    })

    it("should handle timeout errors", async () => {
      // Mock a timeout by delaying the response
      mockFetch.mockImplementationOnce(() => 
        new Promise((resolve) => setTimeout(resolve, 10000))
      )

      const fastClient = createApiClient({ timeout: 100 })

      await expect(fastClient.getHealth()).rejects.toThrow()
    })

    it("should handle HTTP error responses", async () => {
      mockFetch.mockResolvedValueOnce(createMockApiResponse(
        { detail: "Not found" },
        { status: 404, statusText: "Not Found" }
      ))

      await expect(client.getHealth()).rejects.toThrow()
    })
  })

  describe("retry logic", () => {
    it("should retry on server errors", async () => {
      // First call fails with 500, second succeeds
      mockFetch
        .mockResolvedValueOnce(createMockApiResponse(
          { detail: "Server error" },
          { status: 500 }
        ))
        .mockResolvedValueOnce(createMockApiResponse({
          status: "healthy",
          service: "test",
          version: "1.0.0",
          environment: "test",
          timestamp: "2024-01-01T00:00:00Z",
        }))

      const result = await client.getHealth()

      expect(mockFetch).toHaveBeenCalledTimes(2)
      expect(result.status).toBe("healthy")
    })

    it("should not retry on client errors", async () => {
      mockFetch.mockResolvedValueOnce(createMockApiResponse(
        { detail: "Bad request" },
        { status: 400 }
      ))

      await expect(client.getHealth()).rejects.toThrow()
      expect(mockFetch).toHaveBeenCalledTimes(1)
    })

    it("should respect retry limit", async () => {
      // All calls fail with 500
      mockFetch.mockResolvedValue(createMockApiResponse(
        { detail: "Server error" },
        { status: 500 }
      ))

      await expect(client.getHealth()).rejects.toThrow()
      
      // Should be called 1 + retries (2) = 3 times
      expect(mockFetch).toHaveBeenCalledTimes(3)
    })

    it("should disable retry when configured", async () => {
      const noRetryClient = createApiClient({ enableRetry: false })
      
      mockFetch.mockResolvedValueOnce(createMockApiResponse(
        { detail: "Server error" },
        { status: 500 }
      ))

      await expect(noRetryClient.getHealth()).rejects.toThrow()
      expect(mockFetch).toHaveBeenCalledTimes(1)
    })
  })

  describe("request configuration", () => {
    it("should set correct headers", async () => {
      mockFetch.mockResolvedValueOnce(createMockApiResponse({}))

      await client.getHealth()

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            "Content-Type": "application/json",
          }),
        })
      )
    })

    it("should include request body for POST requests", async () => {
      mockFetch.mockResolvedValueOnce(createMockApiResponse({}))

      const request: QueryRequest = { query: "test" }
      await client.query(request)

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          method: "POST",
          body: JSON.stringify(request),
        })
      )
    })
  })

  describe("URL construction", () => {
    it("should construct correct URLs for endpoints", async () => {
      mockFetch.mockResolvedValue(createMockApiResponse({}))

      await client.query({ query: "test" })
      expect(mockFetch).toHaveBeenLastCalledWith(
        "http://localhost:8000/api/query",
        expect.any(Object)
      )

      await client.getStatus()
      expect(mockFetch).toHaveBeenLastCalledWith(
        "http://localhost:8000/api/status",
        expect.any(Object)
      )

      await client.getHealth()
      expect(mockFetch).toHaveBeenLastCalledWith(
        "http://localhost:8000/health",
        expect.any(Object)
      )
    })
  })
})
