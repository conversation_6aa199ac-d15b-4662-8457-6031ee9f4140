/**
 * Query API Hooks
 * 
 * This module provides React Query hooks for natural language query operations,
 * including search and chat interactions with the multi-agent system.
 */

import { useMutation, useQuery, type UseQueryOptions, type UseMutationOptions } from "@tanstack/react-query"

import type { QueryRequest, QueryResponse } from "@/types/api"
import { apiClient } from "@/lib/api"
import { queryKeys } from "@/providers/query-provider"
import { useErrorHandler } from "@/hooks/useErrorHandler"

/**
 * Options for the query mutation hook
 */
export interface UseQueryMutationOptions extends Omit<UseMutationOptions<QueryResponse, Error, QueryRequest>, "mutationFn" | "onSuccess" | "onError"> {
  onSuccess?: (data: QueryResponse, variables: QueryRequest, context?: any) => void
  onError?: (error: Error, variables: QueryRequest, context?: any) => void
  showToast?: boolean
}

/**
 * Options for the query history hook
 */
export interface UseQueryHistoryOptions extends Omit<UseQueryOptions<QueryResponse[], Error>, "queryKey" | "queryFn"> {
  sessionId?: string
  userId?: string
  enabled?: boolean
}

/**
 * Hook for submitting natural language queries
 * 
 * This hook provides a mutation for submitting queries to the multi-agent system
 * with proper error handling, loading states, and optimistic updates.
 * 
 * @param options - Configuration options for the mutation
 * @returns Mutation object with submit function and state
 * 
 * @example
 * ```tsx
 * const queryMutation = useQueryMutation({
 *   onSuccess: (response) => {
 *     console.log('Query successful:', response.result_markdown)
 *   },
 *   onError: (error) => {
 *     console.error('Query failed:', error)
 *   }
 * })
 * 
 * const handleSubmit = () => {
 *   queryMutation.mutate({
 *     query: "How does authentication work?",
 *     session_id: "session-123"
 *   })
 * }
 * ```
 */
export function useQueryMutation(options: UseQueryMutationOptions = {}) {
  const { handleError } = useErrorHandler({
    showToast: options.showToast ?? true,
    defaultContext: { component: "useQueryMutation" },
  })

  return useMutation({
    mutationFn: async (request: QueryRequest): Promise<QueryResponse> => {
      try {
        return await apiClient.query(request)
      } catch (error) {
        handleError(error as Error, { request })
        throw error
      }
    },
    
    // Optimistic updates and cache management
    onMutate: async (variables) => {
      // Cancel any outgoing refetches for query history
      if (variables.session_id) {
        await queryKeys.queries.bySession(variables.session_id)
      }
      
      // Return context for rollback
      return { variables }
    },
    
    onSuccess: (data, variables, context) => {
      // Invalidate and refetch query history if session-based
      if (variables.session_id) {
        // Could add optimistic update to query history here
      }

      // Call custom onSuccess handler
      options.onSuccess?.(data, variables, context)
    },

    onError: (error, variables, context) => {
      // Handle error through global error handler
      handleError(error, { variables, context })

      // Call custom onError handler
      options.onError?.(error, variables, context)
    },
    
    // Retry configuration
    retry: (failureCount, error) => {
      // Don't retry on client errors (4xx)
      if (error && typeof error === "object" && "status" in error) {
        const status = (error as { status: number }).status
        if (status >= 400 && status < 500) {
          return false
        }
      }
      
      // Retry once for server errors
      return failureCount < 1
    },
    
    // Other options
    ...options,
  })
}

/**
 * Hook for fetching query history
 * 
 * This hook provides access to previous queries and responses for a given
 * session or user, enabling conversation history and context.
 * 
 * @param options - Configuration options for the query
 * @returns Query object with history data and state
 * 
 * @example
 * ```tsx
 * const { data: history, isLoading, error } = useQueryHistory({
 *   sessionId: "session-123",
 *   enabled: !!sessionId
 * })
 * ```
 */
export function useQueryHistory(options: UseQueryHistoryOptions = {}) {
  const { sessionId, userId, enabled = true, ...queryOptions } = options
  
  const { handleError } = useErrorHandler({
    showToast: false, // Don't show toast for history fetch errors
    defaultContext: { component: "useQueryHistory" },
  })

  // Determine query key based on available identifiers
  const queryKey = sessionId
    ? queryKeys.queries.bySession(sessionId)
    : userId
    ? queryKeys.queries.byUser(userId)
    : queryKeys.queries.all

  return useQuery({
    queryKey,
    
    queryFn: async (): Promise<QueryResponse[]> => {
      // Note: This would need to be implemented on the backend
      // For now, return empty array as placeholder
      // In a real implementation, this would fetch query history from the backend
      return []
    },
    
    enabled: enabled && (!!sessionId || !!userId),
    
    // Cache configuration
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    
    // Error handling
    onError: (error) => {
      handleError(error, { sessionId, userId })
    },
    
    // Refetch configuration
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    
    ...queryOptions,
  })
}

/**
 * Hook for real-time query suggestions
 * 
 * This hook provides query suggestions based on the current input,
 * helping users formulate better queries.
 * 
 * @param input - Current query input
 * @param options - Configuration options
 * @returns Query object with suggestions
 * 
 * @example
 * ```tsx
 * const { data: suggestions } = useQuerySuggestions(queryInput, {
 *   enabled: queryInput.length > 2,
 *   debounceMs: 300
 * })
 * ```
 */
export function useQuerySuggestions(
  input: string,
  options: {
    enabled?: boolean
    debounceMs?: number
  } = {}
) {
  const { enabled = true, debounceMs = 300 } = options
  
  // Debounce the input
  const [debouncedInput, setDebouncedInput] = React.useState(input)
  
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedInput(input)
    }, debounceMs)
    
    return () => clearTimeout(timer)
  }, [input, debounceMs])

  return useQuery({
    queryKey: ["query-suggestions", debouncedInput],
    
    queryFn: async (): Promise<string[]> => {
      // Note: This would need to be implemented on the backend
      // For now, return static suggestions as placeholder
      if (debouncedInput.length < 2) return []
      
      // Mock suggestions based on input
      const suggestions = [
        `How does ${debouncedInput} work in this codebase?`,
        `What are the best practices for ${debouncedInput}?`,
        `Show me examples of ${debouncedInput} implementation`,
        `What are the dependencies of ${debouncedInput}?`,
      ]
      
      return suggestions.filter(s => 
        s.toLowerCase().includes(debouncedInput.toLowerCase())
      )
    },
    
    enabled: enabled && debouncedInput.length > 1,
    
    // Cache configuration
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    
    // Refetch configuration
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  })
}

/**
 * Hook for query analytics and metrics
 * 
 * This hook provides analytics data about query performance and usage.
 * 
 * @param options - Configuration options
 * @returns Query object with analytics data
 */
export function useQueryAnalytics(options: UseQueryOptions<any, Error> = {}) {
  return useQuery({
    queryKey: ["query-analytics"],
    
    queryFn: async () => {
      // Note: This would fetch analytics from the backend
      // For now, return mock data
      return {
        totalQueries: 0,
        averageResponseTime: 0,
        successRate: 0,
        popularQueries: [],
        errorRate: 0,
      }
    },
    
    // Cache configuration
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    
    // Refetch configuration
    refetchOnWindowFocus: false,
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    
    ...options,
  })
}

/**
 * Utility hook for managing query state
 * 
 * This hook provides utilities for managing query-related state
 * and operations across components.
 */
export function useQueryState() {
  const [currentQuery, setCurrentQuery] = React.useState<string>("")
  const [queryHistory, setQueryHistory] = React.useState<QueryResponse[]>([])
  const [isTyping, setIsTyping] = React.useState(false)
  
  const addToHistory = React.useCallback((response: QueryResponse) => {
    setQueryHistory(prev => [response, ...prev])
  }, [])
  
  const clearHistory = React.useCallback(() => {
    setQueryHistory([])
  }, [])
  
  const updateQuery = React.useCallback((query: string) => {
    setCurrentQuery(query)
    setIsTyping(query.length > 0)
  }, [])
  
  return {
    currentQuery,
    queryHistory,
    isTyping,
    addToHistory,
    clearHistory,
    updateQuery,
    setCurrentQuery,
    setQueryHistory,
    setIsTyping,
  }
}

// Re-export React for hooks
import React from "react"
