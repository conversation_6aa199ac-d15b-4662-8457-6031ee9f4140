/**
 * Ingestion API Hooks
 * 
 * This module provides React Query hooks for repository ingestion operations,
 * including triggering ingestion, monitoring progress, and managing ingestion state.
 */

import React from "react"
import { useMutation, useQuery, useQueryClient, type UseQueryOptions, type UseMutationOptions } from "@tanstack/react-query"

import type { IngestionRequest, IngestionResponse, IngestionStatus } from "@/types/api"
import { apiClient } from "@/lib/api"
import { queryKeys } from "@/providers/query-provider"
import { useErrorHandler } from "@/hooks/useErrorHandler"

/**
 * Options for the ingestion mutation hook
 */
export interface UseIngestionMutationOptions extends Omit<UseMutationOptions<IngestionResponse, Error, IngestionRequest>, "mutationFn"> {
  onSuccess?: (data: IngestionResponse, variables: IngestionRequest) => void
  onError?: (error: Error, variables: IngestionRequest) => void
  onProgress?: (progress: IngestionProgress) => void
}

/**
 * Ingestion progress information
 */
export interface IngestionProgress {
  repository: string
  status: IngestionStatus
  progress: number // 0-100
  currentStep: string
  estimatedTimeRemaining?: number
  filesProcessed?: number
  totalFiles?: number
  chunksCreated?: number
  embeddingsGenerated?: number
}

/**
 * Options for the ingestion status hook
 */
export interface UseIngestionStatusOptions extends Omit<UseQueryOptions<IngestionProgress, Error>, "queryKey" | "queryFn"> {
  repository: string
  enabled?: boolean
  pollingInterval?: number
}

/**
 * Hook for triggering repository ingestion
 * 
 * This hook provides a mutation for starting repository ingestion with
 * progress tracking, error handling, and cache invalidation.
 * 
 * @param options - Configuration options for the mutation
 * @returns Mutation object with ingest function and state
 * 
 * @example
 * ```tsx
 * const ingestionMutation = useIngestionMutation({
 *   onSuccess: (response) => {
 *     console.log('Ingestion completed:', response.status)
 *   },
 *   onError: (error) => {
 *     console.error('Ingestion failed:', error)
 *   },
 *   onProgress: (progress) => {
 *     console.log('Progress:', progress.progress + '%')
 *   }
 * })
 * 
 * const handleIngest = () => {
 *   ingestionMutation.mutate({
 *     repository_url: "https://github.com/owner/repo",
 *     branch: "main",
 *     force_refresh: true
 *   })
 * }
 * ```
 */
export function useIngestionMutation(options: UseIngestionMutationOptions = {}) {
  const queryClient = useQueryClient()
  const { handleError } = useErrorHandler({
    showToast: true,
    defaultContext: { component: "useIngestionMutation" },
  })

  return useMutation({
    mutationFn: async (request: IngestionRequest): Promise<IngestionResponse> => {
      try {
        return await apiClient.ingest(request)
      } catch (error) {
        handleError(error as Error, { request })
        throw error
      }
    },
    
    onMutate: async (variables) => {
      // Cancel any outgoing refetches for this repository
      await queryClient.cancelQueries({
        queryKey: queryKeys.ingestion.byRepository(variables.repository_url)
      })
      
      // Optimistically update ingestion status to "pending"
      queryClient.setQueryData(
        queryKeys.ingestion.status(variables.repository_url),
        {
          repository: variables.repository_url,
          status: "pending" as IngestionStatus,
          progress: 0,
          currentStep: "Initializing ingestion...",
        } as IngestionProgress
      )
      
      return { variables }
    },
    
    onSuccess: (data, variables, context) => {
      // Update ingestion status with completed data
      queryClient.setQueryData(
        queryKeys.ingestion.status(variables.repository_url),
        {
          repository: variables.repository_url,
          status: data.status as IngestionStatus,
          progress: 100,
          currentStep: "Ingestion completed",
          filesProcessed: data.processed_files,
          chunksCreated: data.chunks_created,
          embeddingsGenerated: data.embeddings_generated,
        } as IngestionProgress
      )
      
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: queryKeys.ingestion.all
      })
      
      // Invalidate status queries since new data is available
      queryClient.invalidateQueries({
        queryKey: queryKeys.status.all
      })
      
      // Call custom onSuccess handler
      options.onSuccess?.(data, variables)
    },
    
    onError: (error, variables, context) => {
      // Update ingestion status with error
      queryClient.setQueryData(
        queryKeys.ingestion.status(variables.repository_url),
        {
          repository: variables.repository_url,
          status: "failed" as IngestionStatus,
          progress: 0,
          currentStep: "Ingestion failed",
        } as IngestionProgress
      )
      
      // Handle error through global error handler
      handleError(error, { variables, context })
      
      // Call custom onError handler
      options.onError?.(error, variables)
    },
    
    // Retry configuration - don't retry ingestion automatically
    retry: false,
    
    // Other options
    ...options,
  })
}

/**
 * Hook for monitoring ingestion status and progress
 * 
 * This hook provides real-time status updates for repository ingestion
 * with automatic polling and progress tracking.
 * 
 * @param options - Configuration options for the query
 * @returns Query object with ingestion status and progress
 * 
 * @example
 * ```tsx
 * const { data: progress, isLoading } = useIngestionStatus({
 *   repository: "https://github.com/owner/repo",
 *   enabled: isIngesting,
 *   pollingInterval: 2000
 * })
 * ```
 */
export function useIngestionStatus(options: UseIngestionStatusOptions) {
  const { repository, enabled = true, pollingInterval = 5000, ...queryOptions } = options
  
  const { handleError } = useErrorHandler({
    showToast: false, // Don't show toast for status polling errors
    defaultContext: { component: "useIngestionStatus" },
  })

  return useQuery({
    queryKey: queryKeys.ingestion.status(repository),
    
    queryFn: async (): Promise<IngestionProgress> => {
      // Note: This would need to be implemented on the backend
      // For now, return mock progress data
      // In a real implementation, this would fetch ingestion status from the backend
      
      // Mock progress simulation
      const mockProgress: IngestionProgress = {
        repository,
        status: "completed" as IngestionStatus,
        progress: 100,
        currentStep: "Ingestion completed",
        filesProcessed: 150,
        totalFiles: 150,
        chunksCreated: 1200,
        embeddingsGenerated: 1200,
      }
      
      return mockProgress
    },
    
    enabled: enabled && !!repository,
    
    // Polling configuration
    refetchInterval: (data) => {
      // Stop polling if ingestion is completed or failed
      if (data?.status === "completed" || data?.status === "failed") {
        return false
      }
      return pollingInterval
    },
    
    // Cache configuration
    staleTime: 0, // Always consider stale for real-time updates
    gcTime: 5 * 60 * 1000, // 5 minutes
    
    // Error handling
    onError: (error) => {
      handleError(error, { repository })
    },
    
    // Refetch configuration
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    
    ...queryOptions,
  })
}

/**
 * Hook for fetching ingestion history
 * 
 * This hook provides access to previous ingestion operations and their results.
 * 
 * @param options - Configuration options for the query
 * @returns Query object with ingestion history
 */
export function useIngestionHistory(options: UseQueryOptions<IngestionResponse[], Error> = {}) {
  const { handleError } = useErrorHandler({
    showToast: false,
    defaultContext: { component: "useIngestionHistory" },
  })

  return useQuery({
    queryKey: queryKeys.ingestion.all,
    
    queryFn: async (): Promise<IngestionResponse[]> => {
      // Note: This would need to be implemented on the backend
      // For now, return empty array as placeholder
      return []
    },
    
    // Cache configuration
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    
    // Error handling
    onError: (error) => {
      handleError(error)
    },
    
    // Refetch configuration
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    
    ...options,
  })
}

/**
 * Hook for canceling ongoing ingestion
 * 
 * This hook provides a mutation for canceling in-progress ingestion operations.
 * 
 * @param options - Configuration options for the mutation
 * @returns Mutation object with cancel function
 */
export function useCancelIngestion(options: UseMutationOptions<void, Error, string> = {}) {
  const queryClient = useQueryClient()
  const { handleError } = useErrorHandler({
    showToast: true,
    defaultContext: { component: "useCancelIngestion" },
  })

  return useMutation({
    mutationFn: async (repository: string): Promise<void> => {
      // Note: This would need to be implemented on the backend
      // For now, just simulate cancellation
      console.log("Canceling ingestion for:", repository)
    },
    
    onSuccess: (data, repository) => {
      // Update ingestion status to cancelled
      queryClient.setQueryData(
        queryKeys.ingestion.status(repository),
        {
          repository,
          status: "cancelled" as IngestionStatus,
          progress: 0,
          currentStep: "Ingestion cancelled",
        } as IngestionProgress
      )
      
      // Invalidate ingestion queries
      queryClient.invalidateQueries({
        queryKey: queryKeys.ingestion.all
      })
    },
    
    onError: (error, repository) => {
      handleError(error, { repository })
    },
    
    ...options,
  })
}

/**
 * Utility hook for managing ingestion state
 * 
 * This hook provides utilities for managing ingestion-related state
 * and operations across components.
 */
export function useIngestionState() {
  const [activeIngestions, setActiveIngestions] = React.useState<Set<string>>(new Set())
  const [ingestionHistory, setIngestionHistory] = React.useState<IngestionResponse[]>([])
  
  const startIngestion = React.useCallback((repository: string) => {
    setActiveIngestions(prev => new Set(prev).add(repository))
  }, [])
  
  const stopIngestion = React.useCallback((repository: string) => {
    setActiveIngestions(prev => {
      const next = new Set(prev)
      next.delete(repository)
      return next
    })
  }, [])
  
  const addToHistory = React.useCallback((response: IngestionResponse) => {
    setIngestionHistory(prev => [response, ...prev])
  }, [])
  
  const clearHistory = React.useCallback(() => {
    setIngestionHistory([])
  }, [])
  
  const isIngesting = React.useCallback((repository: string) => {
    return activeIngestions.has(repository)
  }, [activeIngestions])
  
  return {
    activeIngestions: Array.from(activeIngestions),
    ingestionHistory,
    startIngestion,
    stopIngestion,
    addToHistory,
    clearHistory,
    isIngesting,
    setActiveIngestions,
    setIngestionHistory,
  }
}
